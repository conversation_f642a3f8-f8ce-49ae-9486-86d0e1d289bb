<?php  // Moodle configuration file

unset($CFG);
global $CFG;
$CFG = new stdClass();

$CFG->dbtype    = 'mysqli';
$CFG->dblibrary = 'native';
$CFG->dbhost    = getenv('MOODLE_DB_HOST') ?: 'mysql';
$CFG->dbname    = getenv('MOODLE_DB_NAME') ?: 'moodle';
$CFG->dbuser    = getenv('MOODLE_DB_USER') ?: 'moodle';
$CFG->dbpass    = getenv('MOODLE_DB_PASS') ?: '';
$CFG->prefix    = getenv('MOODLE_DB_PREFIX') ?: 'mdl_';
$CFG->dboptions = array (
  'dbpersist' => 0,
  'dbport' => getenv('MOODLE_DB_PORT') ?: 3306,
  'dbsocket' => '',
  'dbcollation' => 'utf8mb4_unicode_ci',
);

$CFG->wwwroot   = getenv('MOODLE_WWWROOT') ?: 'http://localhost';
$CFG->dataroot  = getenv('MOODLE_DATAROOT') ?: '/var/moodledata';
$CFG->admin     = getenv('MOODLE_ADMIN') ?: 'admin';

$CFG->directorypermissions = 0777;

// Session configuration with Redis
$CFG->sessiontimeout = getenv('MOODLE_SESSION_TIMEOUT') ?: 28800; // 8 hours in seconds
$CFG->sessiontimeoutwarning = getenv('MOODLE_SESSION_WARNING') ?: 1200; // 20 minutes warning
$CFG->sessioncookie = getenv('MOODLE_SESSION_COOKIE') ?: 'MoodleSession';
$CFG->sessioncookiepath = '/';
$CFG->sessioncookiedomain = '';
$CFG->sessioncookiesecure = false; // Set to true in production with HTTPS
$CFG->sessioncookiehttponly = true;
$CFG->sessioncookiesamesite = 'Lax';

// Redis session handler configuration (temporarily disabled for troubleshooting)
// $CFG->session_handler_class = '\core\session\redis';
// $CFG->session_redis_host = getenv('MOODLE_REDIS_HOST') ?: 'redis';
// $CFG->session_redis_port = getenv('MOODLE_REDIS_PORT') ?: 6379;
// $CFG->session_redis_database = 0;
// $CFG->session_redis_auth = '';
// $CFG->session_redis_prefix = 'moodle_sess_';
// $CFG->session_redis_acquire_lock_timeout = 120;
// $CFG->session_redis_lock_expire = 7200;

// Performance and debugging
$CFG->cachejs = false;
$CFG->yuicomboloading = false;

// Debugging settings (temporary for troubleshooting)
$CFG->debug = 32767; // DEBUG_DEVELOPER
$CFG->debugdisplay = 1;
$CFG->debugsmtp = 1;

// Additional security and performance settings
$CFG->passwordsaltmain = 'your-random-salt-here-change-this-in-production';
$CFG->preventexecpath = true;
$CFG->disableupdateautodeploy = true;

require_once(__DIR__ . '/lib/setup.php');

// All sensitive/environment-specific values are now loaded from environment variables.
// Set these in your .env file or Docker secrets. Do not hardcode secrets in this file.
// There is no php closing tag in this file,
// it is intentional because it prevents trailing whitespace problems!
